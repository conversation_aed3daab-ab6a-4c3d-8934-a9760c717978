{"name": "angular-realworld", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint --force", "prepare": "husky install"}, "engines": {"node": "^20.11.1"}, "private": true, "dependencies": {"@angular/animations": "18.2.13", "@angular/common": "18.2.13", "@angular/compiler": "18.2.13", "@angular/core": "18.2.13", "@angular/forms": "18.2.13", "@angular/platform-browser": "18.2.13", "@angular/platform-browser-dynamic": "18.2.13", "@angular/router": "18.2.13", "@rx-angular/cdk": "18.0.0", "@rx-angular/template": "18.0.0", "marked": "^11.1.0", "rxjs": "^7.4.0", "tslib": "^2.3.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^18.2.13", "@angular/cli": "^18.2.13", "@angular/compiler-cli": "18.2.13", "@types/jasmine": "~4.3.0", "@types/marked": "^6.0.0", "husky": "^8.0.3", "jasmine-core": "~4.5.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "~5.4.0"}, "lint-staged": {"*.{ts,html,css,json,md}": "prettier --write"}}