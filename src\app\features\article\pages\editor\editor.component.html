<div class="editor-page">
  <div class="container page">
    <div class="row">
      <div class="col-md-10 offset-md-1 col-xs-12">
        <app-list-errors [errors]="errors" />

        <form [formGroup]="articleForm">
          <fieldset [disabled]="isSubmitting">
            <fieldset class="form-group">
              <input
                class="form-control form-control-lg"
                formControlName="title"
                type="text"
                placeholder="Article Title"
              />
            </fieldset>

            <fieldset class="form-group">
              <input
                class="form-control"
                formControlName="description"
                type="text"
                placeholder="What's this article about?"
              />
            </fieldset>

            <fieldset class="form-group">
              <textarea
                class="form-control"
                formControlName="body"
                rows="8"
                placeholder="Write your article (in markdown)"
              >
              </textarea>
            </fieldset>

            <fieldset class="form-group">
              <input
                class="form-control"
                type="text"
                placeholder="Enter tags"
                [formControl]="tagField"
                (keyup.enter)="addTag()"
              />
              <div class="tag-list">
                @for (tag of tagList; track tag) {
                  <span class="tag-default tag-pill">
                    <i class="ion-close-round" (click)="removeTag(tag)"></i>
                    {{ tag }}
                  </span>
                }
              </div>
            </fieldset>

            <button
              class="btn btn-lg pull-xs-right btn-primary"
              type="button"
              (click)="submitForm()"
            >
              Publish Article
            </button>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
</div>
